# Lower Thirds - WebSocket Control via Lua

Cette solution utilise un script Lua dédié pour contrôler les bandeaux via obs-websocket, avec support complet des textes et de l'activation/désactivation.

## 🎯 Avantages de cette approche

- ✅ **Complet** : Gestion des textes ET de l'affichage
- ✅ **Dédié** : Script Lua spécialement conçu pour WebSocket
- ✅ **Fiable** : Communication directe via BroadcastChannel
- ✅ **API simple** : Fonctions claires et documentées

## 🔧 Configuration

### 1. Charger le script Lua WebSocket

1. Dans OBS : **Outils** → **Scripts**
2. Cliquez sur **+** et sélectionnez `lower thirds/lower-thirds-websocket.lua`
3. Le script est maintenant chargé avec toutes les fonctions WebSocket

### 2. Configurer le plugin Lower Thirds original

Assurez-vous que le plugin original fonctionne :

1. **Control Panel** (Browser Dock) :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/lower thirds/control-panel.html
   ```

2. **Browser Source** (dans une scène) :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/lower thirds/browser-source.html
   ```

### 3. Ajouter le bridge JavaScript (optionnel)

Pour une meilleure intégration, ajoutez ce script dans le Control Panel :

```html
<script src="../common/js/websocket_bridge.js"></script>
```

## 🧪 Test

### Test depuis Node.js

```javascript
const LowerThirdsWebSocketController = require('./websocket-bridge/lua-controller');

async function test() {
    const lowerThirds = new LowerThirdsWebSocketController();

    try {
        await lowerThirds.connect();

        // Test avec texte
        await lowerThirds.showLowerThird(1, 'John Doe', 'CEO');

        // Attendre 3 secondes
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Modifier le texte
        await lowerThirds.setLowerThirdText(1, 'Jane Smith', 'CTO');

        // Attendre 2 secondes
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Masquer
        await lowerThirds.hideLowerThird(1);

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

test();
```

### Test avec la séquence complète

```javascript
const LowerThirdsWebSocketController = require('./websocket-bridge/lua-controller');

async function fullTest() {
    const lowerThirds = new LowerThirdsWebSocketController();

    try {
        await lowerThirds.connect();
        await lowerThirds.testSequence(); // Séquence automatique complète
    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

fullTest();
```

## 🔍 Dépannage

### Le script Lua ne fonctionne pas

1. **Vérifiez que le script est chargé** :
   - OBS → Outils → Scripts
   - `lower-thirds-websocket.lua` doit apparaître dans la liste

2. **Vérifiez les logs du script** :
   - Dans la fenêtre Scripts, regardez les logs en bas
   - Activez "Enable Debug Logging" dans les propriétés du script

3. **Testez les fonctions** :
   - Les logs doivent montrer "[LT-WebSocket] ..." quand les fonctions sont appelées

### Le plugin original ne fonctionne pas

1. **Vérifiez la configuration** :
   - Control Panel doit être en Browser Dock
   - Browser Source doit être dans une scène visible

2. **Testez le Control Panel** :
   - Cliquez sur les switches dans le Control Panel
   - Les bandeaux doivent apparaître/disparaître

### Les textes ne s'affichent pas

1. **Vérifiez le fichier bridge** :
   - Le fichier `common/js/websocket_bridge.js` doit être créé
   - Il contient les données envoyées par le script Lua

2. **Vérifiez la console** :
   - Ouvrez les Developer Tools du Control Panel
   - Regardez s'il y a des erreurs BroadcastChannel

## 📋 API Complète

```javascript
const lowerThirds = new LowerThirdsWebSocketController();

// Connexion
await lowerThirds.connect();

// Afficher avec texte
await lowerThirds.showLowerThird(1, 'John Doe', 'CEO');

// Modifier le texte seulement
await lowerThirds.setLowerThirdText(1, 'Jane Smith', 'CTO');

// Toggle on/off
await lowerThirds.toggleLowerThird(1, true);  // show
await lowerThirds.toggleLowerThird(1, false); // hide

// Masquer un bandeau
await lowerThirds.hideLowerThird(1);

// Masquer tous les bandeaux
await lowerThirds.hideAllLowerThirds();

// Obtenir l'état actuel
const state = await lowerThirds.getState();

// Déconnexion
await lowerThirds.disconnect();
```

## 🚀 Fonctionnalités

- ✅ **Activation/désactivation** des bandeaux (1-4)
- ✅ **Gestion des textes** (nom + info)
- ✅ **Fonctions combinées** (show avec texte)
- ✅ **État interne** maintenu dans le script Lua
- ✅ **Communication BroadcastChannel** avec le plugin original
- ✅ **Logs détaillés** pour le debug

Cette solution est complète et prête pour la production !