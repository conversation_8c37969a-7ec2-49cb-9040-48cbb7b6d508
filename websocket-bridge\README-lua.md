# Lower Thirds - Approche Lua Simplifiée

Cette approche utilise le script Lua existant pour contrôler les bandeaux via les hotkeys, ce qui est beaucoup plus simple et fiable.

## 🎯 Avantages de l'approche Lua

- ✅ **Simple** : Utilise l'infrastructure existante
- ✅ **Fiable** : Pas de problèmes de BroadcastChannel
- ✅ **Direct** : Communication directe via hotkeys
- ✅ **Testé** : Le système de hotkeys fonctionne déjà

## 🔧 Configuration

### 1. Charger le script Lua modifié

1. Dans OBS : **Outils** → **Scripts**
2. Cliquez sur **+** et sélectionnez `lower thirds/lower-thirds_hotkeys.lua`
3. Le script est maintenant chargé avec les nouvelles fonctions WebSocket

### 2. Configurer les hotkeys

1. Dans OBS : **Fichier** → **Paramètres** → **Ra<PERSON><PERSON><PERSON> clavier**
2. Cher<PERSON>z "Lower Third Switch" dans la liste
3. Assignez des touches (optionnel, pas obligatoire pour WebSocket)

### 3. Configurer le plugin Lower Thirds original

Assurez-vous que le plugin original fonctionne :

1. **Control Panel** (Browser Dock) :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/lower thirds/control-panel.html
   ```

2. **Browser Source** (dans une scène) :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/lower thirds/browser-source.html
   ```

## 🧪 Test

### Test depuis Node.js

```javascript
const LuaLowerThirdsController = require('./websocket-bridge/lua-controller');

async function test() {
    const lowerThirds = new LuaLowerThirdsController();

    try {
        await lowerThirds.connect();

        // Test simple
        await lowerThirds.showLowerThird(1, 'Test', 'Via Lua');

        // Attendre 3 secondes
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Masquer
        await lowerThirds.hideLowerThird(1);

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

test();
```

### Test avec la séquence complète

```javascript
const LuaLowerThirdsController = require('./websocket-bridge/lua-controller');

async function fullTest() {
    const lowerThirds = new LuaLowerThirdsController();

    try {
        await lowerThirds.connect();
        await lowerThirds.testSequence(); // Séquence automatique
    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

fullTest();
```

## 🔍 Dépannage

### Les hotkeys ne fonctionnent pas

1. **Vérifiez que le script Lua est chargé** :
   - OBS → Outils → Scripts
   - Le script doit apparaître dans la liste

2. **Vérifiez les logs du script** :
   - Dans la fenêtre Scripts, regardez les logs en bas
   - Activez le debug dans les propriétés du script

3. **Testez manuellement** :
   - Assignez une touche à "Lower Third Switch #1"
   - Appuyez sur la touche pour tester

### Le plugin original ne fonctionne pas

1. **Vérifiez la configuration** :
   - Control Panel doit être en Browser Dock
   - Browser Source doit être dans une scène visible

2. **Testez le Control Panel** :
   - Cliquez sur les switches dans le Control Panel
   - Les bandeaux doivent apparaître/disparaître

## 📋 API Simple

```javascript
// Connexion
await lowerThirds.connect();

// Afficher un bandeau (toggle)
await lowerThirds.showLowerThird(1, 'Nom', 'Info');

// Masquer un bandeau
await lowerThirds.hideLowerThird(1);

// Masquer tous les bandeaux
await lowerThirds.hideAllLowerThirds();

// Déconnexion
await lowerThirds.disconnect();
```

## 🚀 Prochaines étapes

1. **Test de base** : Vérifier que les hotkeys fonctionnent
2. **Intégration texte** : Ajouter la gestion des textes (prochaine version)
3. **Slots mémoire** : Support des slots prédéfinis
4. **Configuration avancée** : Couleurs, polices, etc.

Cette approche est beaucoup plus simple et devrait fonctionner immédiatement si le plugin original est correctement configuré.