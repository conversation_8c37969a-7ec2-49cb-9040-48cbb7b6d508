/**
 * Simple Lower Thirds Controller using Lu<PERSON>ript
 * This approach uses the existing Lua script for direct control
 */

const OBSWebSocket = require('obs-websocket-js').default;

class LuaLowerThirdsController {
    constructor() {
        this.obs = new OBSWebSocket();
        this.isConnected = false;
        this.scriptName = 'lower-thirds_hotkeys.lua'; // Nom du script Lua dans OBS
    }

    async connect(address = 'ws://localhost:4455', password = '') {
        try {
            await this.obs.connect(address, password);
            this.isConnected = true;
            console.log('✅ Connected to OBS WebSocket');

            // Listen for events
            this.obs.on('ConnectionClosed', () => {
                console.log('❌ OBS WebSocket connection closed');
                this.isConnected = false;
            });

        } catch (error) {
            console.error('❌ Failed to connect to OBS:', error);
            throw error;
        }
    }

    async disconnect() {
        if (this.isConnected) {
            await this.obs.disconnect();
            this.isConnected = false;
            console.log('👋 Disconnected from OBS WebSocket');
        }
    }

    /**
     * Toggle a lower third on/off using hotkeys (most reliable method)
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {boolean} enabled - true to show, false to hide
     */
    async toggleLowerThird(lowerThirdNumber, enabled) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            // Use hotkey method - most reliable
            const hotkeyName = `A_SWITCH_${lowerThirdNumber}`;
            await this.obs.call('TriggerHotkeyByName', { hotkeyName });
            console.log(`🎬 Toggled Lower Third ${lowerThirdNumber} via hotkey ${hotkeyName}`);

        } catch (error) {
            console.error(`❌ Failed to toggle Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Show a lower third (convenience method)
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text (optional for now)
     * @param {string} info - Info text (optional for now)
     */
    async showLowerThird(lowerThirdNumber, name, info) {
        console.log(`🎬 Showing Lower Third ${lowerThirdNumber}: "${name}" - "${info}"`);

        // For now, just toggle the lower third
        // Text setting will be added in next iteration
        await this.toggleLowerThird(lowerThirdNumber, true);
    }

    /**
     * Hide a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     */
    async hideLowerThird(lowerThirdNumber) {
        console.log(`🚫 Hiding Lower Third ${lowerThirdNumber}`);
        await this.toggleLowerThird(lowerThirdNumber, false);
    }

    /**
     * Hide all lower thirds
     */
    async hideAllLowerThirds() {
        console.log('🚫 Hiding all Lower Thirds');

        const promises = [];
        for (let i = 1; i <= 4; i++) {
            promises.push(this.toggleLowerThird(i, false));
        }
        await Promise.all(promises);
    }

    /**
     * Test sequence
     */
    async testSequence() {
        console.log('🧪 Starting test sequence...');

        try {
            // Show LT1
            await this.showLowerThird(1, 'Test User', 'Via Lua');
            console.log('✅ LT1 shown');

            // Wait 3 seconds
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Hide LT1 and show LT2
            await this.hideLowerThird(1);
            await this.showLowerThird(2, 'Another Test', 'Also Lua');
            console.log('✅ Switched to LT2');

            // Wait 3 seconds
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Hide all
            await this.hideAllLowerThirds();
            console.log('✅ All hidden');

            console.log('🎉 Test sequence completed successfully!');

        } catch (error) {
            console.error('❌ Test sequence failed:', error);
        }
    }
}

// Example usage
async function example() {
    const lowerThirds = new LuaLowerThirdsController();

    try {
        // Connect to OBS
        await lowerThirds.connect();

        // Run test sequence
        await lowerThirds.testSequence();

    } catch (error) {
        console.error('❌ Example failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

// Export the class
module.exports = LuaLowerThirdsController;

// Run example if this file is executed directly
if (require.main === module) {
    example();
}