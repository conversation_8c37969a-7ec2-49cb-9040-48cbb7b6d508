/**
 * Lower Thirds WebSocket Controller
 * Uses the dedicated lower-thirds-websocket.lua script
 */

const OBSWebSocket = require('obs-websocket-js').default;

class LowerThirdsWebSocketController {
    constructor() {
        this.obs = new OBSWebSocket();
        this.isConnected = false;
        this.scriptName = 'lower-thirds-websocket.lua'; // Nom du script Lua WebSocket dédié
    }

    async connect(address = 'ws://localhost:4455', password = '') {
        try {
            await this.obs.connect(address, password);
            this.isConnected = true;
            console.log('✅ Connected to OBS WebSocket');

            // Listen for events
            this.obs.on('ConnectionClosed', () => {
                console.log('❌ OBS WebSocket connection closed');
                this.isConnected = false;
            });

        } catch (error) {
            console.error('❌ Failed to connect to OBS:', error);
            throw error;
        }
    }

    async disconnect() {
        if (this.isConnected) {
            await this.obs.disconnect();
            this.isConnected = false;
            console.log('👋 Disconnected from OBS WebSocket');
        }
    }

    /**
     * Toggle a lower third on/off
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {boolean} enabled - true to show, false to hide
     */
    async toggleLowerThird(lowerThirdNumber, enabled) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            // Use WebSocket hotkeys registered by the Lua script
            const hotkeyName = enabled ? `ws_show_lt${lowerThirdNumber}` : `ws_hide_lt${lowerThirdNumber}`;

            await this.obs.call('TriggerHotkeyByName', {
                hotkeyName: hotkeyName
            });

            console.log(`🎬 Lower Third ${lowerThirdNumber} ${enabled ? 'shown' : 'hidden'} via hotkey ${hotkeyName}`);

        } catch (error) {
            console.error(`❌ Failed to toggle Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Set text for a lower third
     * Note: Text setting via hotkeys is limited. This is a placeholder for future implementation.
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text
     * @param {string} info - Info text
     */
    async setLowerThirdText(lowerThirdNumber, name, info) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        // For now, we can't set text directly via hotkeys
        // This would require a different approach (file-based communication)
        console.log(`📝 Text setting requested for Lower Third ${lowerThirdNumber}: "${name}" - "${info}"`);
        console.log(`⚠️  Note: Text setting via hotkeys is not yet implemented`);

        // TODO: Implement file-based text communication
        return Promise.resolve();
    }

    /**
     * Show a lower third (for now without text setting)
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text (logged but not set yet)
     * @param {string} info - Info text (logged but not set yet)
     */
    async showLowerThird(lowerThirdNumber, name, info) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            // Log the intended text (for future implementation)
            if (name || info) {
                console.log(`📝 Intended text for Lower Third ${lowerThirdNumber}: "${name}" - "${info}"`);
            }

            // Show the lower third using hotkey
            await this.toggleLowerThird(lowerThirdNumber, true);

            console.log(`🎬 Lower Third ${lowerThirdNumber} shown`);

        } catch (error) {
            console.error(`❌ Failed to show Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Hide a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     */
    async hideLowerThird(lowerThirdNumber) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            await this.toggleLowerThird(lowerThirdNumber, false);
            console.log(`🚫 Lower Third ${lowerThirdNumber} hidden`);

        } catch (error) {
            console.error(`❌ Failed to hide Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Hide all lower thirds
     */
    async hideAllLowerThirds() {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            await this.obs.call('TriggerHotkeyByName', {
                hotkeyName: 'ws_hide_all'
            });

            console.log('🚫 All Lower Thirds hidden via hotkey ws_hide_all');

        } catch (error) {
            console.error('❌ Failed to hide all Lower Thirds:', error);
            throw error;
        }
    }

    /**
     * List available WebSocket hotkeys (for debugging)
     */
    async listWebSocketHotkeys() {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            const hotkeys = await this.obs.call('GetHotkeyList');
            const wsHotkeys = hotkeys.hotkeys.filter(h =>
                h.toLowerCase().includes('websocket') || h.toLowerCase().includes('ws_')
            );

            console.log('� Available WebSocket hotkeys:', wsHotkeys);
            return wsHotkeys;

        } catch (error) {
            console.error('❌ Failed to list hotkeys:', error);
            throw error;
        }
    }

    /**
     * Test sequence
     */
    async testSequence() {
        console.log('🧪 Starting WebSocket hotkey test sequence...');

        try {
            // List available hotkeys first
            await this.listWebSocketHotkeys();

            // Show LT1
            await this.showLowerThird(1, 'Test User', 'Via WebSocket Hotkeys');
            console.log('✅ LT1 shown');

            // Wait 3 seconds
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Hide LT1 and show LT2
            await this.hideLowerThird(1);
            await this.showLowerThird(2, 'Second Test', 'Also Hotkeys');
            console.log('✅ Switched to LT2');

            // Wait 3 seconds
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Show both LT1 and LT2
            await this.showLowerThird(1, 'Both Visible', 'LT1');
            console.log('✅ Both LT1 and LT2 visible');

            // Wait 2 seconds
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Hide all using the dedicated hotkey
            await this.hideAllLowerThirds();
            console.log('✅ All hidden');

            console.log('🎉 Test sequence completed successfully!');

        } catch (error) {
            console.error('❌ Test sequence failed:', error);
        }
    }
}

// Example usage
async function example() {
    const lowerThirds = new LowerThirdsWebSocketController();

    try {
        // Connect to OBS
        await lowerThirds.connect();

        // Run test sequence
        await lowerThirds.testSequence();

    } catch (error) {
        console.error('❌ Example failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

// Export the class
module.exports = LowerThirdsWebSocketController;

// Run example if this file is executed directly
if (require.main === module) {
    example();
}