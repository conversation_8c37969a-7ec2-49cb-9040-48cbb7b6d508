/**
 * Lower Thirds WebSocket Controller
 * Uses the dedicated lower-thirds-websocket.lua script
 */

const OBSWebSocket = require('obs-websocket-js').default;

class LowerThirdsWebSocketController {
    constructor() {
        this.obs = new OBSWebSocket();
        this.isConnected = false;
        this.scriptName = 'lower-thirds-websocket.lua'; // Nom du script Lua WebSocket dédié
    }

    async connect(address = 'ws://localhost:4455', password = '') {
        try {
            await this.obs.connect(address, password);
            this.isConnected = true;
            console.log('✅ Connected to OBS WebSocket');

            // Listen for events
            this.obs.on('ConnectionClosed', () => {
                console.log('❌ OBS WebSocket connection closed');
                this.isConnected = false;
            });

        } catch (error) {
            console.error('❌ Failed to connect to OBS:', error);
            throw error;
        }
    }

    async disconnect() {
        if (this.isConnected) {
            await this.obs.disconnect();
            this.isConnected = false;
            console.log('👋 Disconnected from OBS WebSocket');
        }
    }

    /**
     * Toggle a lower third on/off
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {boolean} enabled - true to show, false to hide
     */
    async toggleLowerThird(lowerThirdNumber, enabled) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            // Call Lua function directly
            await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: this.scriptName,
                    function_name: 'toggle_lower_third',
                    arguments: [lowerThirdNumber, enabled]
                }
            });

            console.log(`🎬 Lower Third ${lowerThirdNumber} ${enabled ? 'shown' : 'hidden'}`);

        } catch (error) {
            console.error(`❌ Failed to toggle Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Set text for a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text
     * @param {string} info - Info text
     */
    async setLowerThirdText(lowerThirdNumber, name, info) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: this.scriptName,
                    function_name: 'set_lower_third_text',
                    arguments: [lowerThirdNumber, name || "", info || ""]
                }
            });

            console.log(`📝 Lower Third ${lowerThirdNumber} text set: "${name}" - "${info}"`);

        } catch (error) {
            console.error(`❌ Failed to set text for Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Show a lower third with text
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text
     * @param {string} info - Info text
     */
    async showLowerThird(lowerThirdNumber, name, info) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: this.scriptName,
                    function_name: 'show_lower_third',
                    arguments: [lowerThirdNumber, name || "", info || ""]
                }
            });

            console.log(`🎬 Lower Third ${lowerThirdNumber} shown: "${name}" - "${info}"`);

        } catch (error) {
            console.error(`❌ Failed to show Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Hide a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     */
    async hideLowerThird(lowerThirdNumber) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: this.scriptName,
                    function_name: 'hide_lower_third',
                    arguments: [lowerThirdNumber]
                }
            });

            console.log(`🚫 Lower Third ${lowerThirdNumber} hidden`);

        } catch (error) {
            console.error(`❌ Failed to hide Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Hide all lower thirds
     */
    async hideAllLowerThirds() {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: this.scriptName,
                    function_name: 'hide_all_lower_thirds',
                    arguments: []
                }
            });

            console.log('🚫 All Lower Thirds hidden');

        } catch (error) {
            console.error('❌ Failed to hide all Lower Thirds:', error);
            throw error;
        }
    }

    /**
     * Get current state (for debugging)
     */
    async getState() {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            const response = await this.obs.call('CallVendorRequest', {
                vendorName: 'obs-lua',
                requestType: 'call_function',
                requestData: {
                    script_name: this.scriptName,
                    function_name: 'get_lower_thirds_state',
                    arguments: []
                }
            });

            console.log('📊 Current state:', response);
            return response;

        } catch (error) {
            console.error('❌ Failed to get state:', error);
            throw error;
        }
    }

    /**
     * Test sequence
     */
    async testSequence() {
        console.log('🧪 Starting test sequence...');

        try {
            // Show LT1 with text
            await this.showLowerThird(1, 'Test User', 'Via WebSocket Lua');
            console.log('✅ LT1 shown with text');

            // Wait 3 seconds
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Update text only
            await this.setLowerThirdText(1, 'Updated Name', 'Updated Info');
            console.log('✅ LT1 text updated');

            // Wait 2 seconds
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Hide LT1 and show LT2
            await this.hideLowerThird(1);
            await this.showLowerThird(2, 'Second Test', 'Also WebSocket');
            console.log('✅ Switched to LT2');

            // Wait 3 seconds
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Show both LT1 and LT2
            await this.showLowerThird(1, 'Both Visible', 'LT1');
            console.log('✅ Both LT1 and LT2 visible');

            // Wait 2 seconds
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Hide all
            await this.hideAllLowerThirds();
            console.log('✅ All hidden');

            // Get final state
            await this.getState();

            console.log('🎉 Test sequence completed successfully!');

        } catch (error) {
            console.error('❌ Test sequence failed:', error);
        }
    }
}

// Example usage
async function example() {
    const lowerThirds = new LowerThirdsWebSocketController();

    try {
        // Connect to OBS
        await lowerThirds.connect();

        // Run test sequence
        await lowerThirds.testSequence();

    } catch (error) {
        console.error('❌ Example failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

// Export the class
module.exports = LowerThirdsWebSocketController;

// Run example if this file is executed directly
if (require.main === module) {
    example();
}