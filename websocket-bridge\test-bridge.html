<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lower Thirds WebSocket Bridge Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .control-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        input[type="text"] {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            width: 200px;
        }
        .status {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 3px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Lower Thirds WebSocket Bridge Test</h1>

    <div class="status" id="connectionStatus">
        Status: Initializing...
    </div>

    <div class="control-group">
        <h3>Lower Third 1</h3>
        <button onclick="toggleLowerThird(1, true)">Show LT1</button>
        <button onclick="toggleLowerThird(1, false)">Hide LT1</button>
        <br>
        <input type="text" id="lt1-name" placeholder="Name" value="John Doe">
        <input type="text" id="lt1-info" placeholder="Info" value="CEO">
        <button onclick="setLowerThirdText(1)">Set Text</button>
    </div>

    <div class="control-group">
        <h3>Lower Third 2</h3>
        <button onclick="toggleLowerThird(2, true)">Show LT2</button>
        <button onclick="toggleLowerThird(2, false)">Hide LT2</button>
        <br>
        <input type="text" id="lt2-name" placeholder="Name" value="Jane Smith">
        <input type="text" id="lt2-info" placeholder="Info" value="CTO">
        <button onclick="setLowerThirdText(2)">Set Text</button>
    </div>

    <div class="control-group">
        <h3>Quick Tests</h3>
        <button onclick="testSequence()">Test Sequence</button>
        <button onclick="hideAll()">Hide All</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="control-group">
        <h3>Console Log</h3>
        <div class="log" id="logOutput"></div>
    </div>

    <script src="lower-thirds-websocket.js"></script>
    <script>
        // Override console.log to show in our log div
        const originalLog = console.log;
        const logOutput = document.getElementById('logOutput');

        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logOutput.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
        };

        // Update connection status
        function updateStatus(status) {
            document.getElementById('connectionStatus').textContent = 'Status: ' + status;
        }

        // Control functions
        function toggleLowerThird(number, enabled) {
            if (window.lowerThirdsBridge) {
                window.lowerThirdsBridge.toggleLowerThirdExternal(number, enabled);
                console.log(`Toggled Lower Third ${number}: ${enabled}`);
            } else {
                console.error('Bridge not initialized');
            }
        }

        function setLowerThirdText(number) {
            const nameInput = document.getElementById(`lt${number}-name`);
            const infoInput = document.getElementById(`lt${number}-info`);

            if (window.lowerThirdsBridge && nameInput && infoInput) {
                window.lowerThirdsBridge.setLowerThirdTextExternal(
                    number,
                    nameInput.value,
                    infoInput.value
                );
                console.log(`Set Lower Third ${number} text: ${nameInput.value} - ${infoInput.value}`);
            } else {
                console.error('Bridge not initialized or inputs not found');
            }
        }

        function testSequence() {
            console.log('Starting test sequence...');

            // Set text for LT1
            document.getElementById('lt1-name').value = 'Test User';
            document.getElementById('lt1-info').value = 'Testing';
            setLowerThirdText(1);

            // Show LT1
            setTimeout(() => toggleLowerThird(1, true), 500);

            // Hide LT1 and show LT2
            setTimeout(() => {
                toggleLowerThird(1, false);
                document.getElementById('lt2-name').value = 'Another User';
                document.getElementById('lt2-info').value = 'Also Testing';
                setLowerThirdText(2);
                toggleLowerThird(2, true);
            }, 3000);

            // Hide LT2
            setTimeout(() => toggleLowerThird(2, false), 6000);

            console.log('Test sequence completed');
        }

        function hideAll() {
            for (let i = 1; i <= 4; i++) {
                toggleLowerThird(i, false);
            }
            console.log('All lower thirds hidden');
        }

        function clearLog() {
            logOutput.innerHTML = '';
        }

        // Wait for bridge to initialize
        setTimeout(() => {
            if (window.lowerThirdsBridge) {
                updateStatus('Bridge initialized');
                console.log('Bridge ready for testing');
            } else {
                updateStatus('Bridge failed to initialize');
                console.error('Bridge not available');
            }
        }, 1000);
    </script>
</body>
</html>