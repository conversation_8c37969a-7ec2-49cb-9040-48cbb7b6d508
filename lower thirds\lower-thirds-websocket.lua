--[[
Lower Thirds WebSocket Control Script
Provides WebSocket control for the Animated Lower Thirds plugin
Author: AI Assistant
Version: 1.0
--]]

obs = obslua

-- Script info
function script_description()
	return [[
<h2>Lower Thirds WebSocket Control</h2>
<p>Provides WebSocket control for the Animated Lower Thirds plugin.</p>
<p>Functions available via obs-websocket:</p>
<ul>
<li><strong>toggle_lower_third(number, enabled)</strong> - Show/hide a lower third</li>
<li><strong>set_lower_third_text(number, name, info)</strong> - Set text for a lower third</li>
<li><strong>show_lower_third(number, name, info)</strong> - Show with text</li>
<li><strong>hide_lower_third(number)</strong> - Hide a lower third</li>
<li><strong>hide_all_lower_thirds()</strong> - Hide all lower thirds</li>
</ul>
<p>Use with Node.js obs-websocket library to call these functions.</p>
	]]
end

-- Global variables
local debug = true
local script_path_value = ""

-- Lower thirds state
local lower_thirds_state = {
	[1] = { enabled = false, name = "", info = "" },
	[2] = { enabled = false, name = "", info = "" },
	[3] = { enabled = false, name = "", info = "" },
	[4] = { enabled = false, name = "", info = "" }
}

-- Script properties
function script_properties()
	local props = obs.obs_properties_create()

	obs.obs_properties_add_bool(props, "debug_enabled", "Enable Debug Logging")
	obs.obs_properties_add_text(props, "info_text", "WebSocket Functions", obs.OBS_TEXT_INFO)

	return props
end

-- Script defaults
function script_defaults(settings)
	obs.obs_data_set_bool(settings, "debug_enabled", true)
end

-- Script update
function script_update(settings)
	debug = obs.obs_data_get_bool(settings, "debug_enabled")

	if debug then
		obs.script_log(obs.LOG_INFO, "Lower Thirds WebSocket Control loaded")
	end
end

-- Hotkey IDs
local hotkey_ids = {}

-- Script load
function script_load(settings)
	script_path_value = script_path()

	-- Register hotkeys for WebSocket control
	hotkey_ids.ws_toggle_1 = obs.obs_hotkey_register_frontend("ws_toggle_lt1", "WebSocket Toggle LT1", function() toggle_lower_third(1, not lower_thirds_state[1].enabled) end)
	hotkey_ids.ws_toggle_2 = obs.obs_hotkey_register_frontend("ws_toggle_lt2", "WebSocket Toggle LT2", function() toggle_lower_third(2, not lower_thirds_state[2].enabled) end)
	hotkey_ids.ws_toggle_3 = obs.obs_hotkey_register_frontend("ws_toggle_lt3", "WebSocket Toggle LT3", function() toggle_lower_third(3, not lower_thirds_state[3].enabled) end)
	hotkey_ids.ws_toggle_4 = obs.obs_hotkey_register_frontend("ws_toggle_lt4", "WebSocket Toggle LT4", function() toggle_lower_third(4, not lower_thirds_state[4].enabled) end)

	hotkey_ids.ws_show_1 = obs.obs_hotkey_register_frontend("ws_show_lt1", "WebSocket Show LT1", function() toggle_lower_third(1, true) end)
	hotkey_ids.ws_show_2 = obs.obs_hotkey_register_frontend("ws_show_lt2", "WebSocket Show LT2", function() toggle_lower_third(2, true) end)
	hotkey_ids.ws_show_3 = obs.obs_hotkey_register_frontend("ws_show_lt3", "WebSocket Show LT3", function() toggle_lower_third(3, true) end)
	hotkey_ids.ws_show_4 = obs.obs_hotkey_register_frontend("ws_show_lt4", "WebSocket Show LT4", function() toggle_lower_third(4, true) end)

	hotkey_ids.ws_hide_1 = obs.obs_hotkey_register_frontend("ws_hide_lt1", "WebSocket Hide LT1", function() toggle_lower_third(1, false) end)
	hotkey_ids.ws_hide_2 = obs.obs_hotkey_register_frontend("ws_hide_lt2", "WebSocket Hide LT2", function() toggle_lower_third(2, false) end)
	hotkey_ids.ws_hide_3 = obs.obs_hotkey_register_frontend("ws_hide_lt3", "WebSocket Hide LT3", function() toggle_lower_third(3, false) end)
	hotkey_ids.ws_hide_4 = obs.obs_hotkey_register_frontend("ws_hide_lt4", "WebSocket Hide LT4", function() toggle_lower_third(4, false) end)

	hotkey_ids.ws_hide_all = obs.obs_hotkey_register_frontend("ws_hide_all", "WebSocket Hide All LT", hide_all_lower_thirds)

	if debug then
		obs.script_log(obs.LOG_INFO, "Lower Thirds WebSocket Control initialized")
		obs.script_log(obs.LOG_INFO, "Script path: " .. script_path_value)
		obs.script_log(obs.LOG_INFO, "WebSocket hotkeys registered")
	end
end

-- Script unload
function script_unload()
	if debug then
		obs.script_log(obs.LOG_INFO, "Lower Thirds WebSocket Control unloaded")
	end
end

-- Save hotkeys
function script_save(settings)
	for key, hotkey_id in pairs(hotkey_ids) do
		local hotkey_save_array = obs.obs_hotkey_save(hotkey_id)
		obs.obs_data_set_array(settings, key, hotkey_save_array)
		obs.obs_data_array_release(hotkey_save_array)
	end
end

-- Load hotkeys
function script_load_hotkeys(settings)
	for key, hotkey_id in pairs(hotkey_ids) do
		local hotkey_save_array = obs.obs_data_get_array(settings, key)
		obs.obs_hotkey_load(hotkey_id, hotkey_save_array)
		obs.obs_data_array_release(hotkey_save_array)
	end
end

----------------------------------------------------------
-- Utility Functions
----------------------------------------------------------

-- Log function
local function log(message)
	if debug then
		obs.script_log(obs.LOG_INFO, "[LT-WebSocket] " .. tostring(message))
	end
end

-- Get script directory
local function get_script_dir()
	return script_path_value:match("(.*[/\\])")
end

-- Send data to Control Panel via JavaScript file
local function send_to_control_panel(data)
	local data_dir = get_script_dir() .. "../common/js/"
	local file_path = data_dir .. "websocket_bridge.js"

	-- Create directory if it doesn't exist
	os.execute('mkdir "' .. data_dir:gsub("/", "\\") .. '" 2>nul')

	-- Write data that the Control Panel can read
	local output = io.open(file_path, "w")
	if output then
		output:write('// Lower Thirds WebSocket Bridge Data\n')
		output:write('// Auto-generated by lower-thirds-websocket.lua\n')
		output:write('window.websocketBridgeData = {\n')

		for i = 1, 4 do
			local state = lower_thirds_state[i]
			output:write('  alt_' .. i .. '_name: "' .. (state.name or "") .. '",\n')
			output:write('  alt_' .. i .. '_info: "' .. (state.info or "") .. '",\n')
			output:write('  alt_' .. i .. '_switch: "' .. (state.enabled and "true" or "false") .. '",\n')
		end

		output:write('  timestamp: ' .. os.time() .. ',\n')
		output:write('  source: "websocket-lua"\n')
		output:write('};\n')
		output:write('\n// Auto-trigger BroadcastChannel update\n')
		output:write('if (typeof BroadcastChannel !== "undefined") {\n')
		output:write('  try {\n')
		output:write('    const channel = new BroadcastChannel("obs-lower-thirds-channel");\n')
		output:write('    channel.postMessage(window.websocketBridgeData);\n')
		output:write('    console.log("[WebSocket-Lua] Data sent via BroadcastChannel");\n')
		output:write('  } catch (e) {\n')
		output:write('    console.error("[WebSocket-Lua] BroadcastChannel error:", e);\n')
		output:write('  }\n')
		output:write('}\n')

		output:close()
		log("Data sent to Control Panel: " .. file_path)
		return true
	else
		log("ERROR: Could not write to file: " .. file_path)
		return false
	end
end

----------------------------------------------------------
-- WebSocket API Functions
----------------------------------------------------------

-- Toggle a lower third on/off
function toggle_lower_third(lt_number, enabled)
	if lt_number < 1 or lt_number > 4 then
		log("ERROR: Invalid lower third number: " .. tostring(lt_number))
		return false
	end

	log("Toggle LT" .. lt_number .. " = " .. (enabled and "ON" or "OFF"))

	-- Update internal state
	lower_thirds_state[lt_number].enabled = enabled

	-- Send to Control Panel
	send_to_control_panel()

	return true
end

-- Set text for a lower third
function set_lower_third_text(lt_number, name_text, info_text)
	if lt_number < 1 or lt_number > 4 then
		log("ERROR: Invalid lower third number: " .. tostring(lt_number))
		return false
	end

	log("Set LT" .. lt_number .. " text: '" .. (name_text or "") .. "' - '" .. (info_text or "") .. "'")

	-- Update internal state
	lower_thirds_state[lt_number].name = name_text or ""
	lower_thirds_state[lt_number].info = info_text or ""

	-- Send to Control Panel
	send_to_control_panel()

	return true
end

-- Show a lower third with text (convenience function)
function show_lower_third(lt_number, name_text, info_text)
	if lt_number < 1 or lt_number > 4 then
		log("ERROR: Invalid lower third number: " .. tostring(lt_number))
		return false
	end

	log("Show LT" .. lt_number .. ": '" .. (name_text or "") .. "' - '" .. (info_text or "") .. "'")

	-- Update text first
	if name_text or info_text then
		set_lower_third_text(lt_number, name_text, info_text)
	end

	-- Then show it
	toggle_lower_third(lt_number, true)

	return true
end

-- Hide a lower third
function hide_lower_third(lt_number)
	if lt_number < 1 or lt_number > 4 then
		log("ERROR: Invalid lower third number: " .. tostring(lt_number))
		return false
	end

	log("Hide LT" .. lt_number)

	toggle_lower_third(lt_number, false)

	return true
end

-- Hide all lower thirds
function hide_all_lower_thirds()
	log("Hide all lower thirds")

	for i = 1, 4 do
		lower_thirds_state[i].enabled = false
	end

	-- Send to Control Panel
	send_to_control_panel()

	return true
end

-- Get current state (for debugging)
function get_lower_thirds_state()
	log("Current state requested")

	local state_info = {}
	for i = 1, 4 do
		local state = lower_thirds_state[i]
		state_info[i] = {
			enabled = state.enabled,
			name = state.name,
			info = state.info
		}
	end

	return state_info
end