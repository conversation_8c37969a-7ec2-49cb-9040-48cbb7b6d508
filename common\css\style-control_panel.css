/*********************************
**  Animated Lower Thirds v1.6  **
**  Author: NoeAL               **
*********************************/

:root {
  --interface-size: 1;
  --obs-background: #3a393a;
}


*:focus {outline:none !important}

/*OBS*/
body.browser-source.lower-thirds{
  overflow:hidden;
}

.panel {
	background: var(--obs-background);
	color: var(--main-color);
	font:normal 12px -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
	max-width:100%;
  /*transform: scale(var(--interface-size));
  transform-origin: top;*/
	margin: 8px 3px 30px;
  user-select: none;
  overflow-x: hidden;
}

.static-panel-top {
  width: 330px;
  margin: 5px;
  display: flex;
  flex-direction: column;
}

.panel::-webkit-scrollbar, .panel textarea::-webkit-scrollbar {
  width: var(--obs-scrollbar-thickness);
}
.panel::-webkit-scrollbar-thumb, .panel textarea::-webkit-scrollbar-thumb {
	border-radius: var(--obs-scrollbar-radius);
  border: solid 0px var(--obs-background);
  background-color: var(--obs-scrollbar-color);
  margin: 3px 0;
  padding: 3px 0;
}
.panel::-webkit-scrollbar-button, .panel textarea::-webkit-scrollbar-button {
  height: 3px;
  display: inline-block;
}
/*::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}*/

.panel-bottom {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

/*Main settings panel*/
#alt-main-config-content{
  box-shadow: inset 0px 0px 5px 0px rgb(0 0 0 / 20%);
  border: var(--obs-panel-border);
  border-radius: var(--element-radius);
  padding: 5px;
  margin-bottom: 15px;
  transition: background 0.5s;
}
#alt-main-config-content.active {
  box-shadow: 0px 0px 2px 0px rgb(222, 240, 255),
              0px 0px 12px 0px rgb(0,149,255);
  border-radius: 3px;
}
.main-title{
  width: 275px;
  position: absolute;
}
.main-title.switch-left {
  margin-left: 30px;
}
.main-icon {
  float: left;
  box-sizing: border-box;
  width: 15px;
  height: 15px;
  line-height: 15px;
  font-size: 15px;
  margin: 0px 6px 0px 0px;
}
.main-icon.icon.icon-settings{
  background-color: var(--main-color);
}
#alt-main-config-content i.fas {
  margin: 0px;
}

#global-configuration{
  margin-bottom: -4px;
}
.active + #global-configuration{
  margin-bottom: 0px;
}
#global-configuration #time-options,
#global-configuration #theme-options,
#global-configuration .logos-options,
#global-configuration .default-options,
#global-configuration .bottom-options {
  display: flex;
}
#global-configuration .logos-options,
#global-configuration .default-options,
#global-configuration .bottom-options {
  margin-bottom: 5px;
}
#global-configuration #theme-options #theme {
  width: 100%;
  text-transform: capitalize;
}
.settings-title{
  margin: 0;
  padding: 2px 0;
  width: 24%;
  text-align: right;
}
.settings-inputs {
  width: 76%;
  margin-left: 2px;
  display: flex;
  justify-content: space-between;
}
.settings-inputs > input,
.settings-inputs > select {
  margin-left: 4px!important;
  margin-bottom: 4px;
}
.alt-time-options > .settings-inputs input {
  margin: 0;
}

.default-options > div > label {
  float: left;
  height: 23px;
  padding: 3px;
  box-sizing: border-box;
}
.default-options > div> label > input {
  width: 12px;
  margin: 3px 5px 3px 0;
  float: left;
  height: 12px;
}
.default-options > .settings-inputs {
  flex-direction: column;
}

.accordion {
  cursor: pointer;
  border: none;
  text-align: left;
  outline: none;
  transition: 0.4s;
  position: relative;
  float: right;
  margin: 0px!important;
}
.accordion:after {
  content: "";
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M448 208H304V64c0-18-14-32-32-32h-32c-18 0-32 14-32 32v144H64c-18 0-32 14-32 32v32c0 18 14 32 32 32h144v144c0 18 14 32 32 32h32c18 0 32-14 32-32V304h144c18 0 32-14 32-32v-32C480 222 466 208 448 208z'/%3E%3C/svg%3E");
  font-weight: bold;
  font-size: 20px;
  line-height: 0px;
  margin: 1px 5px 2px;
  display: block;
  position: relative;
  background-color: var(--main-color);
  width: 12px;
  height: 12px;
}
.accordion.active:after {
  content: "";
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M448 208H64c-18 0-32 14-32 32v32c0 18 14 32 32 32h384c18 0 32-14 32-32v-32C480 222 466 208 448 208z'/%3E%3C/svg%3E");
}
.hidable {
  max-height: 0;
  margin-top: 20px;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}
#alt-1-config-content > .accordion > i.fas,
#alt-2-config-content > .accordion > i.fas,
#alt-3-config-content > .accordion > i.fas,
#alt-4-config-content > .accordion > i.fas {
    height: 10px;
    overflow: hidden;
}
.alt-number-icon {
  float: left;
  border-radius: 3px;
  box-sizing: border-box;
  color: var(--main-color);
  width: 15px;
  height: 15px;
  line-height: 11px;
  font-size: 9px;
  font-weight: 900;
  margin: 5px 6px;
  text-align: center;
  border: 2px solid var(--main-color);
}

#add-new-font div {
  margin: 4px 3px 4px 5px;
  background-color: var(--main-color);
  cursor: pointer;
}
#add-new-font div:hover {
  background-color: #ffffff;
}

/*********************/

.more {
  cursor: pointer;
  width: 100%;
  height: 18px;
  line-height: 17px;
  text-align: right;
}
.more div {
  float: right;
  margin: 2px 0px 2px 1px;
  transition: transform 0.2s;
  background-color: var(--main-color);
}
.more.active div {
  transform: rotate(180deg);
  margin-top: 1px;
}
.more:before {
  content: 'Show more';
}
.more.active:before {
  content: 'Show less';
}

.hide-more {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}

/*********************/

#alt-1-config-content,
#alt-2-config-content,
#alt-3-config-content,
#alt-4-config-content {
  box-shadow: inset 0px 0px 5px 0px rgb(0 0 0 / 20%);
  border: var(--obs-panel-border);
  padding: 5px;
  margin-bottom: 15px;
  border-radius: 3px;
  transition: background 0.5s;
}

#alt-1-config-content.active,
#alt-2-config-content.active,
#alt-3-config-content.active,
#alt-4-config-content.active {
  box-shadow: 0px 0px 2px 0px rgb(222, 240, 255),
              0px 0px 12px 0px rgb(0,149,255);
  border-radius: 3px;
}
#alt-1-config-content.inactive,
#alt-2-config-content.inactive,
#alt-3-config-content.inactive,
#alt-4-config-content.inactive {
  box-shadow: 0px 0px 2px 0px rgb(255,231,231),
              0px 0px 12px 0px rgb(255,13,13);
  border-radius: 3px;
}

.panel-bottom > ul {
  margin: 0 0 0.35em;
  list-style: none;
  padding: 0;
  display: flex;
}
.panel-bottom > ul > li div {
  visibility: hidden;
}
.panel-bottom > ul > li {
  display: block;
  box-sizing: border-box;
  float: left;
  border: 1px solid var(--obs-field-background);
  background-color: var(--obs-field-background);
  cursor: pointer;
  width: 17px;
  height: 17px;
  border-radius: 3px;
}
.panel-bottom > ul > li + li {
  margin: 0 0 0 4px;
}
.panel-bottom > ul > li.stored {
  background: #666;
  border: 1px solid #666;
}
.panel-bottom > ul > li.active-slot {
  background: var(--main-color);
  border: 1px solid var(--main-color);
}
.panel-bottom > ul > li.custom-logo::before {
  content: '';
  background-color: var(--main-color);
  width: 2px;
  height: 2px;
  display: block;
  position: absolute;
  margin: 13px 0px 0px 1px;
  border-radius: 50%;
}
.panel-bottom > ul > li.active-slot.custom-logo::before {
  background-color: var(--obs-field-background);
}
.panel-bottom > ul > li.next-to-load {
  animation: blinker-white 0.25s ease-in infinite;
}

@keyframes blinker-white {  
  0% { background-color: var(--main-color); border: 1px #666; }
  50% { background-color: #666; border: 1px solid #666; }
}

#alt-1-name-color, #alt-1-info-color,
#alt-2-name-color, #alt-2-info-color,
#alt-3-name-color, #alt-3-info-color,
#alt-4-name-color, #alt-4-info-color {
  width: 45px;
  height: 15px;
  border: none;
  border-radius: var(--element-radius);
  padding: 3px;
  float: right;
  font-size: 9px;
  transition: 0.1s;
  cursor: pointer;
  text-align: center;
  margin: 4px;
}
#alt-1-name-color.jscolor-active, #alt-1-info-color.jscolor-active,
#alt-2-name-color.jscolor-active, #alt-2-info-color.jscolor-active,
#alt-3-name-color.jscolor-active, #alt-3-info-color.jscolor-active,
#alt-4-name-color.jscolor-active, #alt-4-info-color.jscolor-active {
  cursor: text;
}
.tool-tittle.disabled {
  opacity: 0.2;
}
.tool-tittle.disabled + .jscolor {
  opacity: 0.2;
  pointer-events: none;
}

#alt-1-style-color-1, #alt-1-style-color-2, #alt-1-style-color-3, #alt-1-style-color-4,
#alt-2-style-color-1, #alt-2-style-color-2, #alt-2-style-color-3, #alt-2-style-color-4,
#alt-3-style-color-1, #alt-3-style-color-2, #alt-3-style-color-3, #alt-3-style-color-4,
#alt-4-style-color-1, #alt-4-style-color-2, #alt-4-style-color-3, #alt-4-style-color-4 {
  width: 45px;
  height: 15px;
  border: none;
  border-radius: var(--element-radius);
  padding: 3px;
  font-size: 9px;
  transition: 0.1s;
  cursor: pointer;
  text-align: center;
  margin: 4px 4px 4px 3px;
}

.clean {
    margin: 1px;
    cursor: pointer;
    display: flex;
    width: 9px;
    width: 15px;
    height: 15px;
}
.clean div {
  width: 13px;
  height: 13px;
  margin: 1px;
}
.panel-bottom > .clean > .icon {
    background-color: var(--main-color);
}
.panel-bottom > .clean > .icon:hover {
  background-color: #ffffff;
}
.panel-bottom > label.config-btn {
  width: 15px;
  height: 15px;
  margin: 1px;
  line-height: 0;
}
.panel-bottom > label > .icon-btn {
  margin: 0px;
}
.delete {
  animation: blinker-red 0.25s linear infinite;
}

@keyframes blinker-red {  
  0% { background-color: #1f1e1f; border: 1px solid var(--obs-field-background); }
  50% { background-color: red; border: 1px solid var(--obs-field-background); }
}

.time-bar{
  display: flex;
  flex-direction: column;
  margin: 0 1px 0 0;
}
.time-bar > div {
  float: left;
  font-size: 7px;
  padding: 1px 3px;
  line-height: 1em;
  width: 22px;
  height: 8px;
  background: var(--obs-field-background-disable);
  border-radius: 3px;
  text-align: right;
  box-sizing: border-box;
  overflow: hidden;
}
.time-bar > div + div {
  margin: 1px 0 0 0;
}

label#alt-1-custom-times::after,
label#alt-2-custom-times::after,
label#alt-3-custom-times::after,
label#alt-4-custom-times::after {
  display: none;
}

label#alt-1-custom-times,
label#alt-2-custom-times,
label#alt-3-custom-times,
label#alt-4-custom-times {
  width: 15px;
  height: 15px;
  display: flex;
  margin: 1px!important;
}

#alt-1-custom-times + .hidable,
#alt-2-custom-times + .hidable,
#alt-3-custom-times + .hidable,
#alt-4-custom-times + .hidable {
  margin-top: 0px;
}

label#alt-1-custom-times i.fas,
label#alt-2-custom-times i.fas,
label#alt-3-custom-times i.fas,
label#alt-4-custom-times i.fas {
  font-size: 12px;
  font-weight: 100;
  margin: 0;
  color: #666666;
}
label#alt-1-custom-times.active > .icon-btn,
label#alt-2-custom-times.active > .icon-btn,
label#alt-3-custom-times.active > .icon-btn,
label#alt-4-custom-times.active > .icon-btn {
  background-color: var(--main-color);
}
label#alt-1-custom-times > .icon-btn.active,
label#alt-2-custom-times > .icon-btn.active,
label#alt-3-custom-times > .icon-btn.active,
label#alt-4-custom-times > .icon-btn.active {
  background-color: var(--main-color);
}


/***********************/
/*The switch-the box around the slider*/
.panel label.switch {
  margin: 0px;
  top: 1px;
}
.switch {
  position: relative;
  width: 21px;
  height: 13px;
  float: right;
}
.switch.switch-left {
  float: left;
}
label i.fas {
    font-size: 15px;
    margin: 0 6px 0 2px;
    position: relative;
}
i.fas {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--obs-field-background);
  -webkit-transition: .4s;
  transition: .4s;
}
.slider:before {
  position: absolute;
  content: "";
  height: 9px;
  width: 9px;
  left: 2px;
  bottom: 2px;
  background-color: var(--main-color);
  -webkit-transition: .4s;
  transition: .4s;
}
input:checked + .slider {
  background-color: var(--obs-switch-active-background);
}
/* input:focus + .slider {
  box-shadow: 0 0 1px var(--obs-switch-active-background);
} */
input:checked + .slider:before {
  -webkit-transform: translateX(8px);
  -ms-transform: translateX(8px);
  transform: translateX(8px);
  background-color: #fff;
}
.slider.round {
  border-radius: 14px;
}
.slider.round:before {
  border-radius: 50%;
}

.panel label {
    display: inline-block;
}

.panel input {
	box-sizing: border-box;
	padding:.4em;
	margin:0 0 5px;
	border: var(--obs-field-border);
	display:block;
	width:100%;
  height: 23px;
	border-radius: var(--element-radius);
	background: var(--obs-field-background);
	color: #d2d2d2;
	font-size: 1em;
}
.panel input.disable{
	color: #828282;
	background: #2f2f2f;
}
.panel textarea {
  box-sizing: border-box;
	padding:.4em;
	margin:0 0 5px;
	border: var(--obs-field-border);
	display:block;
	border-radius: var(--element-radius);
	background: var(--obs-field-background);
	color: #d2d2d2;
  font-size: 1em;
  resize: none;
  cursor: auto;
}

::placeholder {
  color: #4a4a4a;
}

/***********************/
input#alt-1-name, input#alt-1-info,
input#alt-2-name, input#alt-2-info,
input#alt-3-name, input#alt-3-info,
input#alt-4-name, input#alt-4-info {
    float: left;
    width: calc(100% - 92px);
    margin-right: 0px;
    border-radius: 2px 0 0 2px;
    border-right: 0;
}
#alt-1-name-appearance, #alt-1-info-appearance,
#alt-2-name-appearance, #alt-2-info-appearance,
#alt-3-name-appearance, #alt-3-info-appearance,
#alt-4-name-appearance, #alt-4-info-appearance {
    float: right;
    background-color: var(--obs-field-background);
    border-radius: 0 var(--element-radius) var(--element-radius) 0;
    border: var(--obs-field-border);
    border-left: 0;
    box-sizing: border-box;
    height: 23px;
    width: 92px;
}

#alt-1-name-appearance > .config-btn > .icon-btn > i.fas,
#alt-1-info-appearance > .config-btn > .icon-btn > i.fas,
#alt-2-name-appearance > .config-btn > .icon-btn > i.fas,
#alt-2-info-appearance > .config-btn > .icon-btn > i.fas,
#alt-3-name-appearance > .config-btn > .icon-btn > i.fas,
#alt-3-info-appearance > .config-btn > .icon-btn > i.fas,
#alt-4-name-appearance > .config-btn > .icon-btn > i.fas,
#alt-4-info-appearance > .config-btn > .icon-btn > i.fas {
    font-size: 13px;
    margin: 5px;
}
.textfields-container > div > label:first-child > span > i.fas {
  transform: translate(0.35em, 0em);
}
.fill-color-appearance,
.border-color-appearance {
    background-color: var(--obs-field-background);
    border-radius: var(--element-radius);
    border: var(--obs-field-border);
    box-sizing: border-box;
    height: 23px;
    width: 155px;
    display: inline-flex;
    justify-content: flex-end;
}
.fill-color-appearance.disable,
.border-color-appearance.disable {
  background-color: #2f2f2f;
  color: #828282;
}
.tool-tittle {
    line-height: 14px;
    font-size: 9px;
    text-transform: uppercase;
    font-weight: 600;
    margin: 4px 0px 4px 0px;
    cursor: default;
}
#alt-1-style-color-1 + .tool-tittle,
#alt-2-style-color-1 + .tool-tittle,
#alt-3-style-color-1 + .tool-tittle,
#alt-4-style-color-1 + .tool-tittle {
    margin-left: 1px;
}
	
.panel button{
	/* background: var(--obs-button-background);
	border: 1px solid var(--obs-button-border); */
	/* color: var(--obs-button-color); */
	padding:.75em;
	margin:0 0 1em;
	text-align:center;
	width:100%;
}

.panel button.onethird{
	width:30%;
	float:left;
	margin-right:2%;
	font-size: 1em;
}

.panel button:hover{
	background: var(--obs-button-hover-background);
	border-color: var(--obs-button-hover-border);
}
.panel button:active{
	/* background: var(--obs-button-active-background); */
}

.panel .radio-position{float: right;text-align:center}

.panel .radio-position input{
	width: 2em;
	height:2em;
	display:inline-block
}

.panel .radio-position input:checked{
	background:red
}
	
.panel ul#predefined {
    list-style: none;
    padding: 0;
}

.panel ul#predefined li .name, ul#predefined li .function {
    display: inline-block;
}	

.panel ul#predefined li {
    padding: .5em 1em;
    border: 1px solid #555;
    margin: 0 0 .25em;
    text-align: center;
    cursor: pointer;
}

.panel ul#predefined li .function:before{ content: "\00a0(";}
.panel ul#predefined li .function:after{ content: ")";}



input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

.number-input {
  display: inline-flex;
  width: 40px;
}
.number-input + .input-no-icon {
    margin-left: 5px;
}
.number-input,
.number-input * {
  box-sizing: border-box;
}
.number-input button {
  outline:none;
  -webkit-appearance: none;
  background-color: transparent;
  border: none;
  align-items: center;
  justify-content: center;
  height: 10px;
  width: 12px;
  padding: 5px 6px;
  cursor: pointer;
  margin: 0;
  bottom: -11px;
  border-radius: 3px;
}
.number-input button.down {
  order: 2;
  right: 13px;
}
.number-input button.up {
  order: 3;
  right: 25px;
  bottom: -2px;
}
.number-input button:hover {
  background-color: #2d2d2d;
}
.number-input button:hover:before,
.number-input button:hover:after {
  background-color: var(--main-color);
}
.number-input button:before,
.number-input button:after {
  display: inline-block;
  position: absolute;
  content: '';
  width: 0.3rem;
  height: 2px;
  background-color: #666666;
  transform: translate(-75%, -50%) rotate(-45deg);
}
.number-input button.up:after {
  transform: translate(-25%, -50%) rotate(45deg);
}
.number-input button.down:before {
  transform: translate(-75%, -50%) rotate(45deg);
}
.number-input button.down:after {
  transform: translate(-25%, -50%) rotate(-45deg);
}

.number-input input[type=number] {
  order: 1;
  text-align: center;
  width: 40px;
  padding-right: 12px;
}
.number-input input[type="number"]:disabled {
  background: #2f2f2f;

}
.number-input input[type="number"]:disabled + button,
.number-input input[type="number"]:disabled + button + button {
    background: #2f2f2f;
    pointer-events: none;
}
.number-input input[type="number"]:disabled + button + button + .input-icon {
    color: #666;
}

.input-icon {
  order: 0;
  display: inline-flex;
  background-color: var(--main-color)!important;
  margin: 4px;
}
.input-icon > .fas {
    font-size: 15px;
    margin: 4px;
}

/* CSS :invalid pseudo selector */
input:invalid {
  /* border: 1px solid;
  border-color: red; */
}

label.config-btn {
    width: 23px;
    height: 23px;
}
label.config-btn.small {
  width: 17px;
  height: 23px;
}
.config-btn input {
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  position: absolute; /* Firefox fix */
}
.icon-btn {
  color: #666666;
  cursor: pointer;
  margin: 4px;
}
.icon-btn:hover {
  background-color: #808080;
}
.icon-btn > .fas {
  font-size: 15px;
  margin: 4px;
  top: 0px;
  cursor: pointer;
}
.icon-btn p {
    display: table;
    font-size: 1.2em;
    font-weight: 600;
    position: relative;
    margin: 0px;
    padding: 0px;
    transform: translate(0.25em, -0.8em);
    line-height: 0;
    cursor: pointer;
}
input:checked + .icon-btn {
  background-color: var(--main-color);
}
input:checked + .icon-btn:hover {
  background-color: #ffffff;
}
input:disabled + .icon-btn {
    background-color: var(--obs-field-background);
    pointer-events: none;
}


.align-toggle {
  display: inline-flex;
}
.align-toggle input {
  position: absolute;
  opacity: 0;
  width: 0;
  display: inline-block;
}
.align-toggle input + label {
  margin: 0;
  width: 21px;
  float:left;
  color: #666666;
  cursor: pointer;
}
.align-toggle input + label > div {
  margin: 4px;
}
.align-toggle input + label > div:hover {
  background-color: #808080;
}
.align-toggle input:checked + label > div {
  background-color: var(--main-color);
}
.align-toggle input:checked + label > div:hover {
  background-color: #ffffff;
}
.align-toggle input:disabled + label > div {
    background-color: var(--obs-field-background);
}
.align-toggle input:disabled + label {
  /* pointer-events: none; */
  cursor: default;
}
.lock-input{
  position: relative;
  width: 0!important;
  left: -23px;
}
select {
  padding: 0.4em;
  font-size: 1em;
  border: var(--obs-field-border);
  border-radius: var(--element-radius);
  height: 23px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23D2D2D2' d='M127 192h257c18 0 27 22 14 34L270 355c-8 8-20 8-28 0L113 226C101 214 110 192 127 192z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: 9px;
  background-position: right 5px center;
  cursor: pointer;
  background-color: var(--obs-field-background);
  color: #d2d2d2;
}
select#alt-1-font,
select#alt-2-font,
select#alt-3-font,
select#alt-4-font {
    width: 155px;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5px;
  margin-bottom: 5px;
}
.grid-row,
.alt-time-options {
  display: inline-flex;
  justify-content: space-between;
  height: 23px;
}
.grid-row > div {
  display: inline-flex;
}
.grid-container * { 
 position: relative;
}
.font-config.group {
    min-width: 143px;
}
/*********************************
**           PREVIEW            **
*********************************/

#alt-preview {
  position: fixed;
  box-sizing: border-box;
  bottom: 0px;
  height: 211px;
  width: 330px;
  padding: 5px;
  margin: 5px 0px 5px;
  border: none;
  border: var(--obs-panel-border);
  border-radius: 3px;
  background: var(--obs-background);
  box-shadow: 0px 0px 5px 0px rgb(0 0 0 / 40%);
  transition: bottom 0.5s;
}

#alt-preview > label > .icon.icon-search {
  background-color: var(--main-color);
}

#alt-preview.active {
  bottom: -190px;
}
#alt-preview.hide {
  display: none;
}

#toggle-preview {
  position: absolute;
  right: 10px;
  top: 6px;
  cursor: pointer;
}
#toggle-preview div{
  transition: transform 0.2s;
  width: 13px;
  height: 13px;
  background-color: var(--main-color);
}
#toggle-preview.active div{
    transform: rotate(-180deg);
}

.embed-container {
  margin-top: 2px;
}
.embed-container iframe {
  /*height: 203px;
  width: 360px;
  border-radius: var(--element-radius);
  border: var(--obs-field-border);
  background: var(--alt-preview-background);
  box-shadow: inset 0px 0px 30px 10px rgb(0 0 0 / 20%);*/
  height: 179px;
  width: 318px;
  border-radius: var(--element-radius);
  border: var(--obs-field-border);
  background: var(--alt-preview-background);
  box-shadow: inset 0px 0px 30px 10px rgb(0 0 0 / 20%);
}

.config-btn.preview-btn {
  float: right;
  margin: 0px 3px;
  width: 16px;
  height: 16px;
}
.config-btn.preview-btn.hide {
  display: none;
}
.config-btn.preview-btn > .icon-btn {
  width: 14px;
  height: 14px;
  margin: 0px;
}

/*********************************/

.sortees {
  background: var(--obs-background);  
}

.ui-sortable-helper {
  box-shadow: -4px 8px 7px rgb(0 0 0 / 40%);
  transform: rotate(-3deg);
  cursor: -webkit-grabbing; cursor: grabbing;
}
.ui-sortable-helper > div {
  /* box-shadow: none!important; */
}

.ui-sortable-placeholder {
  visibility: visible!important;
  border: 2px dashed var(--obs-field-background-disable);
  margin-bottom: 11px;
}

.renameable {
  display: inline-block;
  min-width: 230px;
  position: relative;
  top: 4px;
}
.renameable input {
  display: inline-block;
  background: none;
  margin: 0;
  padding: 0;
  height: 14px;
}

i.alt-icon {
  font-size: 15px;
  margin: 0 6px 0 7px;
  position: relative;
  height: 10px;
  overflow: hidden;
  top: 5px;
}

.drag-handle {
    position: absolute;
    transform: translate(-5px, -5px);
    height: 25px;
    width: 278px;
}
.drag-handle.switch-left {
  margin-left: 30px;
  width: 248px;
}
.drag-handle.switch-left > .renameable {
  min-width: 220px;
}

#fonts-options {
  display: flex;
}

#custom-font-list {
  display: flex;
    margin: 0 0 0 23%;
    padding: 0;
}

#custom-font-list > ul {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  padding: 0;
}

#custom-font-list > ul > li {
  display: flex;
  box-sizing: border-box;
  background: var(--obs-field-background);
  border: var(--obs-field-border);
  width: 100%;
  height: 23px;
  border-radius: 3px;
  margin: 0 0 5px 8px;
}

.font-url-added {display: none;}

.font-name-added {
  width: calc(100% - 23px);
  padding: 2px 4px;
}

.delete-font {
  width: 15px;
  height: 15px;
  margin: 4px 4px 4px 4px;
  padding: 0;
  cursor: pointer;
}

.delete-font div {
  background-color: var(--main-color);
}
.delete-font div:hover {
  background-color: #ffffff;
}
.slot-number {
  visibility: visible!important;
  color: var(--obs-field-background);
  font-weight: 700;
  width: 15px;
  height: 15px;
  margin: 0px;
  padding: 0px;
  font-size: 10px;
  line-height: 15px;
  box-sizing: border-box;
  text-align: center;
}
.slot-number.hide{
  display: none;
}
.stored .slot-number {
  color: var(--main-color);
}
.stored.active-slot .slot-number {
  color: var(--obs-field-background);
}

.panel-bottom > ul > li .tooltiptext {
  visibility: hidden;
  font-size: 11px;
  max-width: 130px;
  background-color: var(--obs-field-background);
  color: var(--main-color);
  text-align: center;
  border-radius: 3px;
  padding: 5px 8px;
  position: absolute;
  z-index: 1;
  margin-top: 25px;
  margin-left: -35px;
  white-space: pre-line;
  opacity: 0;
  transition: opacity 1s;
  box-shadow: 2px 2px 5px rgb(0 0 0 / 30%);
  border: 1px solid #252525;
}
.tooltiptext > hr {
  border: none;
  border-top: 1px solid #333333;
  margin: 7px 0 4px;
}
.tooltiptext > p {
  margin: 0 0 2px 0;
  color: #888888;
  font-size: 0.8em;
}

/*Settings Tooltips*/
.ui-helper-hidden-accessible {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}
.ui-tooltip {
  padding: 5px 8px;
  max-width: 250px;
	position: absolute;
  z-index: 9999;
  white-space: pre-line;
}
.ui-corner-all {
	border-radius: 3px;
}
.ui-widget-content {
	border: 1px solid #c5c5c5;
	background: #ffffff;
	color: var(--obs-field-background);
}
.ui-widget-shadow {
  -webkit-box-shadow: 2px 2px 5px rgb(0 0 0 / 30%);
  box-shadow: 2px 2px 5px rgb(0 0 0 / 30%);
}

.panel-bottom > ul > li:hover .tooltiptext {
  visibility: visible;
  animation: tooltip-anim 0.2s;
  animation-fill-mode: forwards;
  animation-delay: 0.6s;
}
@keyframes tooltip-anim {
  from {opacity: 0;}
  to {opacity: 1;}
}

/************/

.center.hide {
  display: none;
}

.select-image-bg,
.reset-all-bg,
.export-data-bg {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0,0,0,.5);
  z-index: 1;
}
.center {
  height:100%;
  width: 330px;
  display:flex;
  align-items:center;
  justify-content:center;
  position: fixed;
}
.form-input {
  width: 240px;
  padding: 15px;
  background: var(--obs-background);
  border: var(--obs-panel-border);
  pointer-events: all;
  border-radius: 3px;
  box-shadow: 0px 0px 13px 2px rgb(0 0 0 / 70%);
  z-index: 2;
}
.form-input input {
  display:none;
}
.form-input label {
  display: block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: var(--obs-button-background);
  color: var(--main-color);
  font-size: 12px;
  text-transform: Uppercase;
  font-weight: 600;
  border-radius: 3px;
  cursor: pointer;
}
.form-input h1 {
  margin: 0 0 10px;
  line-height: 23px;
  font-size: 1.8em;
  font-weight: 600;
}
.form-input p {
  margin: 0 0 10px;
}

.form-input img {
  width:100%;
  display:none;
  margin:10px 0px;
}

.file-btns,
.reset-btns,
.export-btns {
  display: flex;
}
.btn-remove {
  width: 34px;
  height: 30px;
  line-height: 26px;
  text-align: center;
  background: var(--obs-button-background);
  color: var(--main-color);
  font-size: 12px;
  border-radius: 3px;
  cursor: pointer;
  margin-left: 4px;
}
.btn-remove > div {
  margin: 7px;
}
.btn-ok,
.btn-ok-reset,
.btn-ok-export,
.btn-cancel,
.btn-settings {
  display: block;
  width: 50%;
  height: 26px;
  line-height: 26px;
  text-align: center;
  background: var(--obs-button-background);
  color: var(--main-color);
  font-size: 12px;
  text-transform: Uppercase;
  font-weight: 600;
  border-radius: 3px;
  cursor: pointer;
}
.btn-settings {
  margin-left: 4px;
  text-transform: capitalize;
}
.btn-cancel {
  margin-left: 4px;
}
.btn-ok-export {
  width: 100%;
}
#dataInput {
  display: block;
  width: 240px;
  height: 100%;
  margin-bottom: 15px;
}

.import-file-btn input {
  display: none;
}
.import-file-btn label {
  display: block;
  width: 76px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  background: var(--obs-button-background);
  color: var(--main-color);
  font-size: 12px;
  font-weight: 600;
  border-radius: 3px;
  cursor: pointer;
  margin-left: 4px;
}

.no-image {
  height: 90px;
  border: 2px dashed #555;
  box-sizing: border-box;
  margin: 10px 0px;
  display: flex;
}
.no-image::before {
  content: 'Default';
  margin: auto;
  font-size: 20px;
  font-weight: 700;
  color: #555;
  text-transform: uppercase;
}

.first-edit-container {
  display: flex;
}
.logo-container {
  width: 96px;
  height: 51px;
  background: var(--obs-field-background);
  border-radius: 3px;
  margin-right: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  border: var(--obs-field-border);
  cursor: pointer;
}
.logo-container::after {
  background-color: #00000070;
  width: 100%;
  height: 100%;
  content: '···';
  display: flex;
  margin-left: -100%;
  justify-content: center;
  align-items: center;
  opacity: 0;
  font-size: 22px;
  font-weight: 900;
}
.logo-container:hover::after {
  opacity: 1;
}
.logo-container.default {
  width: 56px;
  height: 56px;
  margin: 0 0 0 4px;
  border: var(--obs-field-border);
}
.logo-numbers {
  display: inline-table;
  position: relative;
  top: 23px;
  left: 6px;
  width: 9px;
  height: 8px;
  background: #1f1e1fa6;
  font-size: 7px;
  box-sizing: border-box;
  border-radius: 50%;
  line-height: 9px;
  text-align: center;
  margin: 0 -5px;
}
.logo-container.hide {
  display: none;
}
#alt-1-logo-default,
#alt-2-logo-default,
#alt-3-logo-default,
#alt-4-logo-default,
#alt-1-logo-preview,
#alt-2-logo-preview,
#alt-3-logo-preview,
#alt-4-logo-preview {
  max-height: 100%;
  max-width: 100%;
}
#alt-1-logo-default,
#alt-2-logo-default,
#alt-3-logo-default,
#alt-4-logo-default {
  margin-left: 1px;
}

.divider {
  height: 1px;
  background-color: var(--obs-scrollbar-color);
  width: 100%;
  margin: 5px 0 10px;
}


input.locked + .icon-btn {
  pointer-events: none;
  user-select: none;
  background-color: var(--main-color);
}

.tab {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  border-bottom: 1px solid var(--obs-tabs-separator);
  margin-bottom: 10px;
}
.tablinks {
  cursor: pointer;
  height: 26px;
  padding: 0px!important;
  margin: 0px!important;
  border-radius: 3px 3px 0 0;
  font: unset;
  color: var(--main-color);
  background: var(--obs-tab-background);
  border: 1px solid var(--obs-tab-border);
  border-bottom: none;
}
.tablinks:not(:last-child) {
  margin-right: 2px!important;
}
.tablinks.active {
  background: var(--obs-tab-active-background);
  border-color: var(--obs-tab-active-border);
}