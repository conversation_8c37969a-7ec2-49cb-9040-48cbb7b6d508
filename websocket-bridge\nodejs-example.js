/**
 * Node.js Example for controlling Lower Thirds via obs-websocket
 * This example shows how to integrate with your existing Node.js project
 */

const OBSWebSocket = require('obs-websocket-js').default;

class LowerThirdsController {
    constructor() {
        this.obs = new OBSWebSocket();
        this.isConnected = false;
    }

    async connect(address = 'ws://localhost:4455', password = '') {
        try {
            await this.obs.connect(address, password);
            this.isConnected = true;
            console.log('Connected to OBS WebSocket');

            // Listen for events
            this.obs.on('ConnectionClosed', () => {
                console.log('OBS WebSocket connection closed');
                this.isConnected = false;
            });

        } catch (error) {
            console.error('Failed to connect to OBS:', error);
            throw error;
        }
    }

    async disconnect() {
        if (this.isConnected) {
            await this.obs.disconnect();
            this.isConnected = false;
            console.log('Disconnected from OBS WebSocket');
        }
    }

    /**
     * Toggle a lower third on/off
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {boolean} enabled - true to show, false to hide
     */
    async toggleLowerThird(lowerThirdNumber, enabled) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            // Send custom vendor request to trigger the lower third
            const response = await this.obs.call('CallVendorRequest', {
                vendorName: 'lower-thirds',
                requestType: 'toggle',
                requestData: {
                    lowerThird: lowerThirdNumber,
                    enabled: enabled
                }
            });

            console.log(`Lower Third ${lowerThirdNumber} ${enabled ? 'shown' : 'hidden'}`);
            return response;

        } catch (error) {
            console.error(`Failed to toggle Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Set text for a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text
     * @param {string} info - Info text
     */
    async setLowerThirdText(lowerThirdNumber, name, info) {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            const response = await this.obs.call('CallVendorRequest', {
                vendorName: 'lower-thirds',
                requestType: 'setText',
                requestData: {
                    lowerThird: lowerThirdNumber,
                    name: name,
                    info: info
                }
            });

            console.log(`Lower Third ${lowerThirdNumber} text set: ${name} - ${info}`);
            return response;

        } catch (error) {
            console.error(`Failed to set text for Lower Third ${lowerThirdNumber}:`, error);
            throw error;
        }
    }

    /**
     * Get current status of all lower thirds
     */
    async getStatus() {
        if (!this.isConnected) {
            throw new Error('Not connected to OBS');
        }

        try {
            const response = await this.obs.call('CallVendorRequest', {
                vendorName: 'lower-thirds',
                requestType: 'getStatus',
                requestData: {}
            });

            console.log('Lower Thirds status:', response);
            return response;

        } catch (error) {
            console.error('Failed to get Lower Thirds status:', error);
            throw error;
        }
    }

    /**
     * Convenience method to show a lower third with text
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text
     * @param {string} info - Info text
     */
    async showLowerThird(lowerThirdNumber, name, info) {
        await this.setLowerThirdText(lowerThirdNumber, name, info);
        await this.toggleLowerThird(lowerThirdNumber, true);
    }

    /**
     * Hide a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     */
    async hideLowerThird(lowerThirdNumber) {
        await this.toggleLowerThird(lowerThirdNumber, false);
    }

    /**
     * Hide all lower thirds
     */
    async hideAllLowerThirds() {
        const promises = [];
        for (let i = 1; i <= 4; i++) {
            promises.push(this.hideLowerThird(i));
        }
        await Promise.all(promises);
        console.log('All lower thirds hidden');
    }
}

// Example usage
async function example() {
    const lowerThirds = new LowerThirdsController();

    try {
        // Connect to OBS
        await lowerThirds.connect();

        // Show Lower Third 1
        await lowerThirds.showLowerThird(1, 'John Doe', 'CEO');

        // Wait 3 seconds
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Hide Lower Third 1 and show Lower Third 2
        await lowerThirds.hideLowerThird(1);
        await lowerThirds.showLowerThird(2, 'Jane Smith', 'CTO');

        // Wait 3 seconds
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Hide all
        await lowerThirds.hideAllLowerThirds();

        // Get status
        await lowerThirds.getStatus();

    } catch (error) {
        console.error('Example failed:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

// Export the class for use in other modules
module.exports = LowerThirdsController;

// Run example if this file is executed directly
if (require.main === module) {
    example();
}