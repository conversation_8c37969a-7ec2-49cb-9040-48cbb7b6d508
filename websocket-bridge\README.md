# Lower Thirds WebSocket Bridge

Ce pont WebSocket permet de contrôler le plugin "Animated Lower Thirds" via obs-websocket depuis votre projet Node.js.

## Architecture

Le pont fonctionne en interceptant les communications entre le Control Panel et le Browser Source via les BroadcastChannels existants, tout en exposant une interface WebSocket pour le contrôle externe.

```
Node.js Project → obs-websocket → WebSocket Bridge → BroadcastChannel → Lower Thirds Plugin
```

## Installation

1. Copiez le dossier `websocket-bridge` dans votre projet Lower Thirds
2. Assurez-vous qu'OBS WebSocket est activé (port 4455 par défaut)
3. Ajoutez le script bridge dans OBS comme Browser Source

## Configuration dans OBS

### Étape 1: Ajouter le pont comme Browser Source

1. Dans OBS, ajoutez une nouvelle **Browser Source**
2. Configurez l'URL vers le fichier `test-bridge.html` :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/websocket-bridge/test-bridge.html
   ```
3. Largeur: 800, Hauteur: 600
4. Co<PERSON>z "Shutdown source when not visible" et "Refresh browser when scene becomes active"

### Étape 2: Configurer le plugin Lower Thirds existant

1. Ajoutez le **Control Panel** comme **Browser Dock** :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/lower thirds/control-panel.html
   ```

2. Ajoutez le **Browser Source** pour l'affichage :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/lower thirds/browser-source.html
   ```

## Utilisation depuis Node.js

### Installation des dépendances

```bash
npm install obs-websocket-js
```

### Exemple d'utilisation

```javascript
const LowerThirdsController = require('./websocket-bridge/nodejs-example');

async function main() {
    const lowerThirds = new LowerThirdsController();

    try {
        // Connexion à OBS
        await lowerThirds.connect();

        // Afficher un bandeau
        await lowerThirds.showLowerThird(1, 'John Doe', 'CEO');

        // Attendre 3 secondes
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Masquer le bandeau
        await lowerThirds.hideLowerThird(1);

    } catch (error) {
        console.error('Erreur:', error);
    } finally {
        await lowerThirds.disconnect();
    }
}

main();
```

## API

### `toggleLowerThird(number, enabled)`
- `number`: 1, 2, 3, ou 4
- `enabled`: true pour afficher, false pour masquer

### `setLowerThirdText(number, name, info)`
- `number`: 1, 2, 3, ou 4
- `name`: Texte du nom
- `info`: Texte d'information

### `showLowerThird(number, name, info)`
Méthode de convenance qui définit le texte et affiche le bandeau

### `hideLowerThird(number)`
Masque un bandeau spécifique

### `hideAllLowerThirds()`
Masque tous les bandeaux

## Test

1. Ouvrez OBS avec le plugin Lower Thirds configuré
2. Ajoutez la Browser Source du pont WebSocket
3. Ouvrez la page de test dans un navigateur :
   ```
   file:///C:/Utile/Animated-Lower-Thirds/websocket-bridge/test-bridge.html
   ```
4. Utilisez les boutons pour tester les fonctionnalités

## Dépannage

### Le pont ne se connecte pas à OBS
- Vérifiez que OBS WebSocket est activé (Outils → WebSocket Server Settings)
- Vérifiez le port (4455 par défaut)
- Vérifiez s'il y a un mot de passe configuré

### Les bandeaux ne s'affichent pas
- Vérifiez que le Control Panel et Browser Source du plugin original sont bien configurés
- Vérifiez la console du navigateur pour les erreurs BroadcastChannel
- Assurez-vous que les deux Browser Sources sont dans le même contexte (même origine)

### Erreurs de CORS
- Utilisez des URLs `file://` ou servez les fichiers via un serveur HTTP local
- Assurez-vous que tous les fichiers sont accessibles depuis le même domaine

## Limitations actuelles

- Seuls les textes et l'activation/désactivation sont supportés
- Les paramètres visuels (couleurs, polices, etc.) ne sont pas encore exposés via WebSocket
- Nécessite que le plugin original soit configuré et fonctionnel

## Prochaines étapes

- [ ] Support des slots mémoire
- [ ] Configuration visuelle via WebSocket
- [ ] Événements de statut en temps réel
- [ ] Support des hotkeys via WebSocket