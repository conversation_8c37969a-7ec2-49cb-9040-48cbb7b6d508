# Animated-Lower-Thrids
Animated Lower Thirds with dockable Control Panel - OBS Tool

With this tool you can use a control panel to add and change your own lower thirds on the fly. Feel free to customize. Basic knowledge of HTML, Javascript and CSS is required.
For communication between control panel and browser layer it use BroadcastChannel API.

1. Download the Zip file, unzip it.
2. See how to install and new features: https://youtu.be/tddMYWya7O0
3. See previus features: https://youtu.be/cQ0_1GwpwB0

Unfortunately, OBS doesn't support browser panels on macOs. It only works on Windows.

This project is based on Lower thirds in HTML/CSS https://obsproject.com/forum/resources/lower-thirds-in-html-css.928/, and Animated lower thirds with control panel https://obsproject.com/forum/resources/animated-lower-thirds-with-control-panel.922/.


I am a designer and my scripting knowledge is few. I made this tool (Frankenststool) because I needed it and I want to share it. You are welcome to improve it. I am aware that many parts of the code can make any expert cry. I'm really sorry :P


## Donations.
If you like the extension and you want to support the development - please consider to donate by [Paypal](https://paypal.me/noealdac). Any donations are greatly appreciated.

## License.
The Animated Lower Thirds source code is made available under the [MIT license](https://github.com/noeal-dac/Animated-Lower-Thrids/blob/master/LICENSE).
