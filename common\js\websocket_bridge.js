// Lower Thirds WebSocket Bridge Data
// Auto-generated by lower-thirds-websocket.lua
window.websocketBridgeData = {
  alt_1_name: "",
  alt_1_info: "",
  alt_1_switch: "false",
  alt_2_name: "",
  alt_2_info: "",
  alt_2_switch: "false",
  alt_3_name: "",
  alt_3_info: "",
  alt_3_switch: "false",
  alt_4_name: "",
  alt_4_info: "",
  alt_4_switch: "false",
  timestamp: 1751381431,
  source: "websocket-lua"
};

// Auto-trigger BroadcastChannel update
if (typeof BroadcastChannel !== "undefined") {
  try {
    const channel = new BroadcastChannel("obs-lower-thirds-channel");
    channel.postMessage(window.websocketBridgeData);
    console.log("[WebSocket-Lua] Data sent via BroadcastChannel");
  } catch (e) {
    console.error("[WebSocket-Lua] BroadcastChannel error:", e);
  }
}
