/**
 * Lower Thirds WebSocket Bridge
 * Connects obs-websocket to the existing Lower Thirds plugin via BroadcastChannel
 * Author: AI Assistant
 * Version: 1.0
 */

class LowerThirdsWebSocketBridge {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;

        // BroadcastChannels to communicate with existing plugin
        this.controlChannel = new BroadcastChannel('obs-lower-thirds-channel');
        this.statusChannel = new BroadcastChannel('obs-lower-thirds-channel2');

        // Current state of lower thirds
        this.currentState = {
            alt_1_switch: "false",
            alt_2_switch: "false",
            alt_3_switch: "false",
            alt_4_switch: "false",
            alt_1_name: "",
            alt_1_info: "",
            alt_2_name: "",
            alt_2_info: "",
            alt_3_name: "",
            alt_3_info: "",
            alt_4_name: "",
            alt_4_info: ""
        };

        this.init();
    }

    init() {
        this.connectWebSocket();
        this.setupBroadcastChannelListeners();
        console.log('Lower Thirds WebSocket Bridge initialized');
    }

    connectWebSocket() {
        try {
            // Default OBS WebSocket connection
            this.ws = new WebSocket('ws://localhost:4455');

            this.ws.onopen = () => {
                console.log('Connected to OBS WebSocket');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.authenticate();
            };

            this.ws.onmessage = (event) => {
                this.handleWebSocketMessage(JSON.parse(event.data));
            };

            this.ws.onclose = () => {
                console.log('Disconnected from OBS WebSocket');
                this.isConnected = false;
                this.scheduleReconnect();
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };

        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            this.scheduleReconnect();
        }
    }

    authenticate() {
        // For OBS WebSocket 5.x, authentication might be required
        // This is a basic implementation - adjust based on your OBS setup
        const authMessage = {
            op: 1, // Hello
            d: {
                rpcVersion: 1
            }
        };
        this.sendWebSocketMessage(authMessage);
    }

    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay}ms`);
            setTimeout(() => this.connectWebSocket(), this.reconnectDelay);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }

    sendWebSocketMessage(message) {
        if (this.ws && this.isConnected) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket not connected, message not sent:', message);
        }
    }

    handleWebSocketMessage(message) {
        console.log('Received WebSocket message:', message);

        // Handle different message types
        if (message.op === 0) { // Hello message
            console.log('Received Hello from OBS WebSocket');
        } else if (message.op === 6) { // Request response
            this.handleRequestResponse(message);
        } else if (message.op === 5) { // Event
            this.handleEvent(message);
        } else if (message.op === 9) { // Custom request (our lower thirds commands)
            this.handleLowerThirdsRequest(message);
        }
    }

    handleLowerThirdsRequest(message) {
        const { requestId, requestData } = message.d;
        console.log('Lower Thirds request received:', requestData);

        try {
            this.processLowerThirdsCommand(requestData);

            // Send success response
            this.sendWebSocketMessage({
                op: 7, // RequestResponse
                d: {
                    requestId: requestId,
                    requestStatus: {
                        result: true,
                        code: 100
                    },
                    responseData: { success: true }
                }
            });
        } catch (error) {
            console.error('Error processing Lower Thirds command:', error);

            // Send error response
            this.sendWebSocketMessage({
                op: 7, // RequestResponse
                d: {
                    requestId: requestId,
                    requestStatus: {
                        result: false,
                        code: 500,
                        comment: error.message
                    }
                }
            });
        }
    }

    handleRequestResponse(message) {
        const { requestId, requestStatus, responseData } = message.d;
        console.log(`Request ${requestId} status:`, requestStatus);

        if (requestStatus.result === false) {
            console.error('Request failed:', requestStatus.comment);
        }
    }

    handleEvent(message) {
        const { eventType, eventData } = message.d;
        console.log(`Event received: ${eventType}`, eventData);

        // Handle specific events if needed
        if (eventType === 'VendorEvent') {
            this.handleVendorEvent(eventData);
        }
    }

    handleVendorEvent(eventData) {
        if (eventData.vendorName === 'lower-thirds') {
            console.log('Lower Thirds vendor event:', eventData);
            this.processLowerThirdsCommand(eventData.eventData);
        }
    }

    setupBroadcastChannelListeners() {
        // Listen to status updates from the browser source (reduce spam)
        this.statusChannel.onmessage = (event) => {
            // Only log status updates every 10 seconds to reduce spam
            if (!this.lastStatusLog || Date.now() - this.lastStatusLog > 10000) {
                console.log('Status update from Lower Thirds (reduced logging):', event.data);
                this.lastStatusLog = Date.now();
            }
        };
    }

    // Main method to process Lower Thirds commands
    processLowerThirdsCommand(commandData) {
        const { command, lowerThird, data } = commandData;

        switch (command) {
            case 'toggle':
                this.toggleLowerThird(lowerThird, data.enabled);
                break;
            case 'setText':
                this.setLowerThirdText(lowerThird, data.name, data.info);
                break;
            case 'getStatus':
                this.sendStatus();
                break;
            default:
                console.warn('Unknown command:', command);
        }
    }

    toggleLowerThird(lowerThirdNumber, enabled) {
        console.log(`Toggling Lower Third ${lowerThirdNumber}: ${enabled}`);

        const switchKey = `alt_${lowerThirdNumber}_switch`;
        this.currentState[switchKey] = enabled ? "true" : "false";

        // Send updated state to the control panel via BroadcastChannel
        this.controlChannel.postMessage(this.currentState);
    }

    setLowerThirdText(lowerThirdNumber, name, info) {
        console.log(`Setting Lower Third ${lowerThirdNumber} text: ${name} - ${info}`);

        const nameKey = `alt_${lowerThirdNumber}_name`;
        const infoKey = `alt_${lowerThirdNumber}_info`;

        this.currentState[nameKey] = name || "";
        this.currentState[infoKey] = info || "";

        // Send updated state to the control panel via BroadcastChannel
        this.controlChannel.postMessage(this.currentState);
    }

    sendStatus() {
        // Send current status back via WebSocket
        const statusMessage = {
            op: 5, // Event
            d: {
                eventType: 'VendorEvent',
                eventData: {
                    vendorName: 'lower-thirds',
                    eventType: 'status',
                    eventData: this.currentState
                }
            }
        };
        this.sendWebSocketMessage(statusMessage);
    }

    // Public API methods that can be called externally

    /**
     * Toggle a lower third on/off
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {boolean} enabled - true to show, false to hide
     */
    toggleLowerThirdExternal(lowerThirdNumber, enabled) {
        this.toggleLowerThird(lowerThirdNumber, enabled);
    }

    /**
     * Set text for a lower third
     * @param {number} lowerThirdNumber - 1, 2, 3, or 4
     * @param {string} name - Name text
     * @param {string} info - Info text
     */
    setLowerThirdTextExternal(lowerThirdNumber, name, info) {
        this.setLowerThirdText(lowerThirdNumber, name, info);
    }
}

// Initialize the bridge when the script loads
let lowerThirdsBridge;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBridge);
} else {
    initBridge();
}

function initBridge() {
    lowerThirdsBridge = new LowerThirdsWebSocketBridge();

    // Expose bridge to global scope for external access
    window.lowerThirdsBridge = lowerThirdsBridge;

    // Add test functions for easy debugging
    window.testLT = {
        show1: () => lowerThirdsBridge.showLowerThird(1, 'Test User', 'Via Console'),
        hide1: () => lowerThirdsBridge.hideLowerThird(1),
        show2: () => lowerThirdsBridge.showLowerThird(2, 'Another Test', 'Also Console'),
        hide2: () => lowerThirdsBridge.hideLowerThird(2),
        hideAll: () => lowerThirdsBridge.hideAllLowerThirds(),
        status: () => console.log('Current state:', lowerThirdsBridge.currentState)
    };

    console.log('Lower Thirds WebSocket Bridge ready');
    console.log('Test commands available: testLT.show1(), testLT.hide1(), testLT.show2(), testLT.hide2(), testLT.hideAll(), testLT.status()');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LowerThirdsWebSocketBridge;
}